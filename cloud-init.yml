#cloud-config
# Script de inicialização da VM Linux

package_upgrade: true

packages:
  - curl
  - wget
  - git
  - htop
  - unzip
  - nginx
  - ufw
  - fail2ban

# Configurar firewall
runcmd:
  - systemctl enable nginx
  - systemctl start nginx
  - ufw --force enable
  - ufw allow ssh
  - ufw allow 'Nginx Full'
  - systemctl enable fail2ban
  - systemctl start fail2ban
  - echo "VM Linux configurada com sucesso!" > /var/log/cloud-init-custom.log

# Criar usuário adicional (opcional)
users:
  - name: ${admin_username}
    groups: sudo
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']

# Configurar timezone
timezone: America/Sao_Paulo

# Configurar locale
locale: pt_BR.UTF-8

# Mensagem de boas-vindas
final_message: |
  Sistema inicializado com sucesso!
  Acesse via SSH: ssh ${admin_username}@<IP_PUBLICO>
  Nginx está rodando na porta 80
  
write_files:
  - path: /var/www/html/index.html
    content: |
      <!DOCTYPE html>
      <html>
      <head>
          <title>VM Linux no Azure</title>
          <style>
              body { font-family: Arial, sans-serif; margin: 40px; }
              .container { max-width: 800px; margin: 0 auto; }
              .header { background: #0078d4; color: white; padding: 20px; border-radius: 5px; }
          </style>
      </head>
      <body>
          <div class="container">
              <div class="header">
                  <h1>🚀 VM Linux no Azure</h1>
                  <p>Sua máquina virtual Linux foi configurada com sucesso!</p>
              </div>
              <h2>Informações do Sistema</h2>
              <ul>
                  <li><strong>Sistema:</strong> Ubuntu 20.04 LTS</li>
                  <li><strong>Servidor Web:</strong> Nginx</li>
                  <li><strong>Firewall:</strong> UFW ativo</li>
                  <li><strong>Proteção:</strong> Fail2ban configurado</li>
              </ul>
              <h2>Próximos Passos</h2>
              <p>Conecte-se via SSH para começar a configurar sua aplicação:</p>
              <code>ssh ${admin_username}@&lt;IP_PUBLICO&gt;</code>
          </div>
      </body>
      </html>
    permissions: '0644'
