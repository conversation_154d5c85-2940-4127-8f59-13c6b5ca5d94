output "resource_group_name" {
  description = "Nome do Resource Group criado"
  value       = azurerm_resource_group.main.name
}

output "vm_name" {
  description = "Nome da máquina virtual"
  value       = azurerm_linux_virtual_machine.main.name
}

output "vm_id" {
  description = "ID da máquina virtual"
  value       = azurerm_linux_virtual_machine.main.id
}

output "public_ip_address" {
  description = "Endereço IP público da máquina virtual"
  value       = azurerm_public_ip.main.ip_address
}

output "private_ip_address" {
  description = "Endereço IP privado da máquina virtual"
  value       = azurerm_network_interface.main.private_ip_address
}

output "ssh_connection_command" {
  description = "Comando para conectar via SSH"
  value       = "ssh ${var.admin_username}@${azurerm_public_ip.main.ip_address}"
}

output "vm_fqdn" {
  description = "FQDN da máquina virtual (se configurado)"
  value       = azurerm_public_ip.main.fqdn
}

output "network_security_group_id" {
  description = "ID do Network Security Group"
  value       = azurerm_network_security_group.main.id
}

output "virtual_network_id" {
  description = "ID da Virtual Network"
  value       = azurerm_virtual_network.main.id
}

output "subnet_id" {
  description = "ID da Subnet"
  value       = azurerm_subnet.internal.id
}
