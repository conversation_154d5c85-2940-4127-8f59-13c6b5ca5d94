# Terraform - Deploy VM Linux no Azure

Este projeto contém a configuração completa do Terraform para deploy de uma máquina virtual Linux no Microsoft Azure.

## 📋 Pré-requisitos

1. **Azure CLI** instalado e configurado
2. **Terraform** instalado (versão >= 1.0)
3. **Chave SSH** gerada (`ssh-keygen -t rsa -b 4096`)
4. **Subscription do Azure** ativa

## 🚀 Como usar

### 1. Preparação inicial

```bash
# Clone ou baixe os arquivos do projeto
# Navegue até o diretório do projeto

# Faça login no Azure
az login

# Configure a subscription (se necessário)
az account set --subscription "sua-subscription-id"
```

### 2. Configuração das variáveis

```bash
# Copie o arquivo de exemplo
cp terraform.tfvars.example terraform.tfvars

# Edite o arquivo terraform.tfvars com seus valores
nano terraform.tfvars
```

**Importante:** Altere pelo menos:
- `allowed_ssh_ips` - Coloque seu IP público para segurança
- `ssh_public_key_path` - Caminho para sua chave pública SSH

### 3. Deploy da infraestrutura

```bash
# Inicializar o Terraform
terraform init

# Planejar as mudanças
terraform plan

# Aplicar as mudanças
terraform apply
```

### 4. Conectar à VM

Após o deploy, use o comando exibido no output:

```bash
# O comando será algo como:
ssh azureuser@<IP_PUBLICO>
```

## 📁 Estrutura dos arquivos

- `main.tf` - Recursos principais (VM, rede, segurança)
- `variables.tf` - Definição das variáveis
- `outputs.tf` - Outputs do Terraform
- `versions.tf` - Versões dos providers
- `cloud-init.yml` - Script de inicialização da VM
- `terraform.tfvars.example` - Exemplo de configuração

## 🔧 Recursos criados

- **Resource Group** - Grupo de recursos
- **Virtual Network** - Rede virtual com subnet
- **Network Security Group** - Regras de firewall
- **Public IP** - IP público estático
- **Network Interface** - Interface de rede
- **Linux Virtual Machine** - VM Ubuntu 20.04 LTS

## 🛡️ Segurança

- SSH configurado com chaves (sem senha)
- Firewall (UFW) ativo
- Fail2ban para proteção contra ataques
- Network Security Group com regras específicas
- Acesso SSH restrito por IP

## 💰 Custos

A configuração padrão usa `Standard_B2s` que custa aproximadamente:
- ~$30-40/mês (pode variar por região)

Para reduzir custos, altere `vm_size` para `Standard_B1s`.

## 🧹 Limpeza

Para remover todos os recursos:

```bash
terraform destroy
```

## 📝 Customizações

### Alterar tamanho da VM
```hcl
vm_size = "Standard_B1s"  # Mais barato
vm_size = "Standard_D2s_v3"  # Mais potente
```

### Alterar distribuição Linux
```hcl
vm_image = {
  publisher = "RedHat"
  offer     = "RHEL"
  sku       = "8-LVM"
  version   = "latest"
}
```

### Adicionar disco de dados
Adicione no `main.tf`:
```hcl
resource "azurerm_managed_disk" "data" {
  name                 = "${var.vm_name}-data-disk"
  location             = azurerm_resource_group.main.location
  resource_group_name  = azurerm_resource_group.main.name
  storage_account_type = "Premium_LRS"
  create_option        = "Empty"
  disk_size_gb         = 100
}
```

## 🆘 Troubleshooting

### Erro de autenticação SSH
- Verifique se o caminho da chave pública está correto
- Confirme se a chave foi gerada corretamente: `ls -la ~/.ssh/`

### VM não acessível
- Verifique se seu IP está na lista `allowed_ssh_ips`
- Confirme se o Network Security Group permite SSH na porta 22

### Erro de quota
- Verifique os limites da sua subscription no Azure
- Tente uma região diferente

## 📞 Suporte

Para dúvidas sobre:
- **Terraform**: [Documentação oficial](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs)
- **Azure**: [Documentação Azure](https://docs.microsoft.com/azure/)
