# Copie este arquivo para terraform.tfvars e ajuste os valores conforme necessário

# Configurações básicas
resource_group_name = "rg-minha-vm-linux"
location           = "East US"
vm_name            = "vm-linux-prod"
vm_size            = "Standard_B2s"

# Configurações de acesso
admin_username       = "azureuser"
ssh_public_key_path  = "~/.ssh/id_rsa.pub"
allowed_ssh_ips      = ["SEU_IP_AQUI/32"] # Substitua pelo seu IP público

# Configurações da VM
vm_image = {
  publisher = "Canonical"
  offer     = "0001-com-ubuntu-server-focal"
  sku       = "20_04-lts-gen2"
  version   = "latest"
}

disk_size_gb = 50

# Tags
tags = {
  Environment = "Production"
  Project     = "WebServer"
  Owner       = "DevOps-Team"
  CostCenter  = "IT-001"
}
