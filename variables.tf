variable "resource_group_name" {
  description = "Nome do Resource Group"
  type        = string
  default     = "rg-vm-linux"
}

variable "location" {
  description = "Localização dos recursos no Azure"
  type        = string
  default     = "East US"
}

variable "vm_name" {
  description = "Nome da máquina virtual"
  type        = string
  default     = "vm-linux-01"
}

variable "vm_size" {
  description = "Tamanho da máquina virtual"
  type        = string
  default     = "Standard_B2s"
}

variable "admin_username" {
  description = "Nome do usuário administrador"
  type        = string
  default     = "azureuser"
}

variable "ssh_public_key_path" {
  description = "Caminho para a chave pública SSH"
  type        = string
  default     = "~/.ssh/id_rsa.pub"
}

variable "allowed_ssh_ips" {
  description = "Lista de IPs permitidos para SSH"
  type        = list(string)
  default     = ["0.0.0.0/0"] # ATENÇÃO: Altere para seu IP específico em produção
}

variable "vm_image" {
  description = "Imagem da máquina virtual"
  type = object({
    publisher = string
    offer     = string
    sku       = string
    version   = string
  })
  default = {
    publisher = "Canonical"
    offer     = "0001-com-ubuntu-server-focal"
    sku       = "20_04-lts-gen2"
    version   = "latest"
  }
}

variable "disk_size_gb" {
  description = "Tamanho do disco OS em GB"
  type        = number
  default     = 30
}

variable "tags" {
  description = "Tags para os recursos"
  type        = map(string)
  default = {
    Environment = "Development"
    Project     = "Linux-VM"
    Owner       = "DevOps"
  }
}
